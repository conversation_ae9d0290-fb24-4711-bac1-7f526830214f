import { create } from "zustand";
import { devtools } from "zustand/middleware";

export interface ProjectCreationProgress {
  id: string;
  message: string;
  completed: boolean;
  timestamp: number;
}

export interface QuestionnaireData {
  isFirstTimeFounder: boolean | null;
  stage: "idea" | "building" | "launched" | "revenue" | null;
  timeWorking:
    | "<1day"
    | "1-7days"
    | "7-30days"
    | "30-90days"
    | "90+days"
    | null;
  projectName: string;
}

export interface ProjectCreationState {
  // Input state
  ideaText: string;

  // Questionnaire state
  showQuestionnaire: boolean;
  currentQuestionIndex: number;
  questionnaireData: QuestionnaireData;
  questionnaireCompleted: boolean;

  // Creation flow state
  isCreating: boolean;
  currentStep:
    | "idle"
    | "questionnaire"
    | "creating"
    | "simulated_onboarding"
    | "expanding"
    | "completed";
  currentProgress: ProjectCreationProgress | null;
  totalSteps: number;
  completedSteps: number;

  // Animation state
  showLogo: boolean;
  logoRotating: boolean;
  showGlow: boolean;
  circleExpanding: boolean;
  showCompletionMessage: boolean;

  // Project data
  createdProject: {
    id: string;
    name: string;
    description: string;
  } | null;

  // Error state
  error: string | null;
}

export interface ProjectCreationActions {
  // Input actions
  setIdeaText: (text: string) => void;

  // Questionnaire actions
  startQuestionnaire: () => void;
  setQuestionnaireAnswer: (field: keyof QuestionnaireData, value: any) => void;
  nextQuestion: () => void;
  previousQuestion: () => void;
  completeQuestionnaire: () => void;

  // Creation flow actions
  startCreation: () => void;
  startSimulatedOnboarding: () => void;
  setCurrentProgress: (progress: ProjectCreationProgress) => void;
  setTotalSteps: (total: number) => void;
  incrementCompletedSteps: () => void;
  setCurrentStep: (step: ProjectCreationState["currentStep"]) => void;

  // Animation actions
  setLogoRotating: (rotating: boolean) => void;
  setShowGlow: (show: boolean) => void;
  setCircleExpanding: (expanding: boolean) => void;
  setShowCompletionMessage: (show: boolean) => void;

  // Project actions
  setCreatedProject: (project: ProjectCreationState["createdProject"]) => void;

  // Utility actions
  reset: () => void;
  setError: (error: string | null) => void;
}

export interface ProjectCreationStore
  extends ProjectCreationState,
    ProjectCreationActions {}

const initialState: ProjectCreationState = {
  ideaText: "",
  showQuestionnaire: false,
  currentQuestionIndex: 0,
  questionnaireData: {
    isFirstTimeFounder: null,
    stage: null,
    timeWorking: null,
    projectName: "",
  },
  questionnaireCompleted: false,
  isCreating: false,
  currentStep: "idle",
  currentProgress: null,
  totalSteps: 0,
  completedSteps: 0,
  showLogo: true,
  logoRotating: false,
  showGlow: false,
  circleExpanding: false,
  showCompletionMessage: false,
  createdProject: null,
  error: null,
};

export const useProjectCreationStore = create<ProjectCreationStore>()(
  devtools(
    (set, get) => ({
      ...initialState,

      // Input actions
      setIdeaText: (text: string) => set({ ideaText: text }),

      // Questionnaire actions
      startQuestionnaire: () => {
        console.log(
          '📋 Starting questionnaire - setting currentStep to "questionnaire"'
        );
        set({
          showQuestionnaire: true,
          currentStep: "questionnaire",
          currentQuestionIndex: 0,
          questionnaireCompleted: false,
        });
      },

      setQuestionnaireAnswer: (field: keyof QuestionnaireData, value: any) => {
        set((state) => ({
          questionnaireData: {
            ...state.questionnaireData,
            [field]: value,
          },
        }));
      },

      nextQuestion: () => {
        set((state) => ({
          currentQuestionIndex: Math.min(state.currentQuestionIndex + 1, 3),
        }));
      },

      previousQuestion: () => {
        set((state) => ({
          currentQuestionIndex: Math.max(state.currentQuestionIndex - 1, 0),
        }));
      },

      completeQuestionnaire: () => {
        const state = get();

        set({
          questionnaireCompleted: true,
          showQuestionnaire: false,
          // Don't set currentStep to "creating" since project should already be created
          // The project creation should have been started when project name was entered
        });

        // If project is already created (from background process), we're done
        // If not, we'll wait for the background process to complete
      },

      // Creation flow actions
      startCreation: () => {
        set({
          isCreating: true,
          currentStep: "creating",
          currentProgress: null,
          totalSteps: 0,
          completedSteps: 0,
          logoRotating: true,
          showGlow: true,
          error: null,
        });
      },

      startSimulatedOnboarding: () => {
        console.log("🏪 Store: startSimulatedOnboarding called");
        set({
          currentStep: "simulated_onboarding",
          isCreating: false,
          error: null,
        });
        console.log("🏪 Store: currentStep set to simulated_onboarding");
      },

      setCurrentProgress: (progress: ProjectCreationProgress) => {
        set({ currentProgress: progress });
      },

      setTotalSteps: (total: number) => {
        set({ totalSteps: total });
      },

      incrementCompletedSteps: () => {
        set((state) => ({ completedSteps: state.completedSteps + 1 }));
      },

      setCurrentStep: (step: ProjectCreationState["currentStep"]) => {
        set({ currentStep: step });
      },

      // Animation actions
      setLogoRotating: (rotating: boolean) => set({ logoRotating: rotating }),
      setShowGlow: (show: boolean) => set({ showGlow: show }),
      setCircleExpanding: (expanding: boolean) =>
        set({ circleExpanding: expanding }),
      setShowCompletionMessage: (show: boolean) =>
        set({ showCompletionMessage: show }),

      // Project actions
      setCreatedProject: (project: ProjectCreationState["createdProject"]) => {
        set({ createdProject: project });
      },

      // Utility actions
      reset: () => set(initialState),
      setError: (error: string | null) => set({ error }),
    }),
    {
      name: "project-creation-store",
    }
  )
);

// Export actions for easier access
export const projectCreationActions = () => useProjectCreationStore.getState();
