"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { useAiIntake } from "@/hooks/useAiIntake";
import { useAnalytics } from "@/hooks/useAnalytics";
import { useClerkAuth } from "@/hooks/useClerkAuth";
import { authApi } from "@/lib/api";
import { useProjectCreationStore } from "@/stores/projectCreationStore";
import { usePromptStore } from "@/stores/promptStore";
import { ArrowRight, Sparkles, Target, Users, Zap } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";

interface ProjectInputSectionProps {
  variant: "dashboard" | "landing";
  title?: string;
  subtitle?: string;
  showWelcomeBadge?: boolean;
  showQuickActions?: boolean;
  showFeatureBadges?: boolean;
}

export function ProjectInputSection({
  variant,
  title,
  subtitle,
  showWelcomeBadge = false,
  showQuickActions = false,
  showFeatureBadges = false,
}: ProjectInputSectionProps) {
  const { isSignedIn } = useClerkAuth();
  const { trackClick, trackCustomEvent } = useAnalytics();
  const router = useRouter();
  const { savePrompt } = usePromptStore();

  const [currentHintIndex, setCurrentHintIndex] = useState(0);
  const [displayedText, setDisplayedText] = useState("");
  const [isTyping, setIsTyping] = useState(true);
  const [projectInput, setProjectInput] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Project creation store
  const {
    setIdeaText,
    startQuestionnaire,
    startSimulatedOnboarding,
    setCreatedProject,
    setBackgroundProjectReady,
  } = useProjectCreationStore();

  // AI intake hook
  const { startIntake } = useAiIntake();

  const hints = [
    "share your business idea & I'll help bring it into reality",
    "Describe your next project idea",
    "What do you want to build?",
    "What's on your mind?",
  ];

  // Typing animation effect
  useEffect(() => {
    const currentHint = hints[currentHintIndex];
    let timeoutId: NodeJS.Timeout;

    if (isTyping) {
      if (displayedText.length < currentHint.length) {
        timeoutId = setTimeout(() => {
          setDisplayedText(currentHint.slice(0, displayedText.length + 1));
        }, 50);
      } else {
        timeoutId = setTimeout(() => {
          setIsTyping(false);
        }, 500);
      }
    } else {
      if (displayedText.length > 0) {
        timeoutId = setTimeout(() => {
          setDisplayedText(displayedText.slice(0, -1));
        }, 25);
      } else {
        setCurrentHintIndex((prev) => (prev + 1) % hints.length);
        setIsTyping(true);
      }
    }

    return () => clearTimeout(timeoutId);
  }, [displayedText, isTyping, currentHintIndex, hints]);

  const handleSubmit = () => {
    if (!projectInput.trim()) return;

    if (variant === "dashboard") {
      // Dashboard: Start simulated onboarding directly
      setIsSubmitting(true);
      setIdeaText(projectInput);

      // Create a mock project and start simulated onboarding
      const mockProject = {
        id: `mock-${Date.now()}`,
        name: projectInput.trim() || "My Project",
        description: projectInput.trim(),
      };

      console.log(
        "🚀 Starting simulated onboarding with project:",
        mockProject
      );

      setCreatedProject(mockProject);
      startSimulatedOnboarding();

      // Reset loading state immediately since we're showing simulation
      setTimeout(() => {
        setIsSubmitting(false);
      }, 100);

      // Start real project creation in background
      setTimeout(async () => {
        try {
          console.log("🚀 Creating real project in background...");
          const project = await authApi.createProject({
            name: projectInput.trim() || "My Project",
            description: projectInput.trim(),
          });

          console.log("✅ Real project created:", project);

          // Update with real project data
          const realProject = {
            id:
              (project as any).id ||
              (project as any).projectId ||
              `${Date.now()}`,
            name: (project as any).name,
            description: (project as any).description,
          };

          setCreatedProject(realProject);

          // Mark background project as ready
          setBackgroundProjectReady(true);
          console.log("🎉 Background project is ready!");

          // Kick off AI intake
          const projId = (project as any).id || (project as any).projectId;
          if (projId) {
            void startIntake(String(projId), projectInput.trim());
          }
        } catch (e) {
          console.error("Failed to create project via backend:", e);
        }
      }, 1000);
    } else {
      // Landing: Analytics and routing
      trackClick("generate-project-button", "hero-section");
      trackCustomEvent("project_generation_attempted", {
        user_signed_in: isSignedIn,
        project_input_length: projectInput.length,
        has_project_input: projectInput.trim().length > 0,
        redirect_destination: isSignedIn ? "dashboard" : "register",
      });

      if (isSignedIn && projectInput.trim()) {
        console.log("🚀 Starting project creation flow from landing page");
        setIdeaText(projectInput);
        startQuestionnaire();
        const promptId = savePrompt(projectInput.trim());
        console.log("📝 Saved prompt with ID:", promptId);
        console.log("🔄 Navigating to /projects/create");
        router.push(
          `/projects/create?promptId=${encodeURIComponent(promptId)}`
        );
      } else if (isSignedIn) {
        console.log("🔄 User signed in but no input, redirecting to dashboard");
        router.push("/user-dashboard");
      } else {
        console.log("🔄 User not signed in, redirecting to register");
        router.push("/auth/register");
      }
    }
  };

  const setQuickAction = (text: string, type: string) => {
    if (variant === "landing") {
      trackClick("quick-action-badge", "hero-section");
      trackCustomEvent("badge_clicked", {
        badge_type: type,
        location: "hero-section",
      });
    }
    setProjectInput(text);
  };

  // Theme-based styling
  const isDashboard = variant === "dashboard";
  const cardBg = isDashboard
    ? "bg-[#166534]/5 hover:bg-[#166534]/8"
    : "bg-card/80";
  const gradientOverlay = isDashboard
    ? "from-[#166534]/8 via-transparent to-[#22c55e]/8"
    : "from-[var(--primary)]/5 via-transparent to-[var(--primary-light)]/5";
  const inputBg = isDashboard
    ? "bg-[#166534]/5 hover:bg-[#166534]/10"
    : "bg-background/50";
  const inputBorder = isDashboard
    ? "border-1 border-[#166534] hover:border-[#166534]/50 "
    : "border";
  const inputFocus = isDashboard
    ? "focus:ring-[#26F000]/40 focus:border-[#166534]"
    : "focus:ring-[var(--primary)]/20 focus:border-[var(--primary)]";
  const buttonBg = isDashboard
    ? "bg-[#CEFFC5] hover:bg-[#26F000] text-black hover:text-black"
    : "bg-[var(--primary)] hover:bg-[var(--primary-dark)] text-[var(--primary-foreground)]";

  return (
    <div className="w-full max-w-sm mx-auto">
      {/* Header Content */}
      {(title || subtitle || showWelcomeBadge) && (
        <div className="mb-8 text-center">
          {showWelcomeBadge && (
            <div className="flex items-center justify-center gap-2 mb-4">
              <Badge
                variant="secondary"
                className="bg-[#166534]/10 text-[#166534] border-[#166534]/20"
              >
                <Sparkles className="w-3 h-3 mr-1" />
                Welcome back
              </Badge>
            </div>
          )}
          {title && (
            <h1 className="text-xl md:text-2xl lg:text-4xl font-regular text-foreground mb-6 leading-tight">
              {title}
            </h1>
          )}
          {subtitle && (
            <p className="text-lg md:text-xl leading-relaxed tracking-tight text-muted-foreground max-w-2xl mx-auto">
              {subtitle}
            </p>
          )}
        </div>
      )}

      {/* Input Card */}
      <div
        className={`group relative ${cardBg} backdrop-blur border-2 border-[#166534]/90 rounded-2xl p-6 space-y-4 shadow-xl overflow-hidden transition-all duration-300`}
      >
        {/* Gradient overlay */}
        <div
          className={`absolute inset-0 bg-gradient-to-br ${gradientOverlay} pointer-events-none`}
        />

        <div className="relative z-10">
          {/* Target label for landing variant */}

          {/* Input area */}
          <div className="relative">
            <textarea
              value={projectInput}
              onChange={(e) => {
                setProjectInput(e.target.value);
                if (variant === "landing" && e.target.value.length === 1) {
                  trackCustomEvent("project_input_started", {
                    location: "hero-section",
                  });
                }
              }}
              onFocus={() => {
                if (variant === "landing") {
                  trackCustomEvent("project_input_focused", {
                    location: "hero-section",
                  });
                }
              }}
              placeholder={displayedText}
              className={`w-full px-4 py-3 pr-16 rounded-lg ${inputBg} ${inputBorder} backdrop-blur text-foreground placeholder:text-muted-foreground focus:outline-none focus:ring-2 ${inputFocus} resize-none min-h-[80px] transition-all duration-300 hover:shadow-lg relative z-10`}
              rows={3}
              disabled={isSubmitting}
            />
            <Button
              onClick={handleSubmit}
              disabled={!projectInput.trim() || isSubmitting}
              className={`absolute right-3 bottom-4 ${buttonBg} shadow-lg z-20 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300`}
              size="icon"
            >
              {isSubmitting ? (
                <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin" />
              ) : (
                <ArrowRight className="w-4 h-4" />
              )}
            </Button>
          </div>

          {/* Feature badges for dashboard */}
          {showFeatureBadges && (
            <div className="flex items-center justify-center gap-4 text-xs text-muted-foreground mt-4">
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse" />
                AI-powered insights
              </span>
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-blue-500 rounded-full animate-pulse" />
                Smart project planning
              </span>
              <span className="flex items-center gap-1">
                <div className="w-2 h-2 bg-purple-500 rounded-full animate-pulse" />
                Team collaboration
              </span>
            </div>
          )}

          {/* Quick action badges for landing */}
          {showQuickActions && (
            <div className="flex flex-wrap gap-2 justify-center mt-4">
              <Badge
                variant="outline"
                className="text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer"
                onClick={() =>
                  setQuickAction(
                    "Create a project tracker that manages team tasks and deadlines",
                    "project_tracker"
                  )
                }
              >
                <Zap className="w-3 h-3 mr-1" />
                Project tracker
              </Badge>
              <Badge
                variant="outline"
                className="text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer"
                onClick={() =>
                  setQuickAction(
                    "Create a team collaboration platform for better communication",
                    "team_collaboration"
                  )
                }
              >
                <Users className="w-3 h-3 mr-1" />
                Team collaboration
              </Badge>
              <Badge
                variant="outline"
                className="text-xs hover:bg-[var(--primary-light)] hover:border-[var(--primary)] transition-colors cursor-pointer"
                onClick={() =>
                  setQuickAction(
                    "Create a goal management system to track objectives and milestones",
                    "goal_management"
                  )
                }
              >
                <Target className="w-3 h-3 mr-1" />
                Goal management
              </Badge>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
